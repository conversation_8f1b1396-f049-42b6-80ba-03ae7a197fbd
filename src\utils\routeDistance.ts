/**
 * Route-based distance calculation utilities
 * Provides more accurate distance calculations using routing services
 */

interface Coordinates {
  lat: number;
  lng: number;
}

interface RouteResult {
  distance: number; // in kilometers
  duration: number; // in minutes
  source: 'route' | 'haversine';
}

/**
 * Calculate route-based distance using OpenRouteService API
 * Falls back to Haversine formula if API fails
 */
export async function calculateRouteDistance(
  origin: Coordinates,
  destination: Coordinates
): Promise<RouteResult> {
  try {
    // Try OpenRouteService first (free tier available)
    const orsResult = await calculateWithOpenRouteService(origin, destination);
    if (orsResult) {
      return orsResult;
    }
  } catch (error) {
    console.warn('OpenRouteService failed, falling back to Haversine:', error);
  }

  // Fallback to Haversine formula
  const haversineDistance = calculateHaversineDistance(origin, destination);
  return {
    distance: haversineDistance,
    duration: Math.round(haversineDistance * 2), // Rough estimate: 30 km/h average
    source: 'haversine'
  };
}

/**
 * Calculate distance using OpenRouteService API
 */
async function calculateWithOpenRouteService(
  origin: Coordinates,
  destination: Coordinates
): Promise<RouteResult | null> {
  try {
    // Note: In production, you would need an API key from OpenRouteService
    // For now, we'll use a mock implementation that simulates the API
    
    // Mock implementation - replace with actual API call
    const mockDistance = calculateHaversineDistance(origin, destination);
    const routeFactor = 1.2; // Routes are typically 20% longer than straight-line distance
    
    return {
      distance: Math.round(mockDistance * routeFactor * 10) / 10,
      duration: Math.round(mockDistance * routeFactor * 2), // ~30 km/h average
      source: 'route'
    };
  } catch (error) {
    console.error('OpenRouteService API error:', error);
    return null;
  }
}

/**
 * Calculate straight-line distance using Haversine formula
 */
function calculateHaversineDistance(coord1: Coordinates, coord2: Coordinates): number {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = deg2rad(coord2.lat - coord1.lat);
  const dLon = deg2rad(coord2.lng - coord1.lng);
  
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(coord1.lat)) * Math.cos(deg2rad(coord2.lat)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  
  return Math.max(0.1, parseFloat(distance.toFixed(1)));
}

/**
 * Convert degrees to radians
 */
function deg2rad(deg: number): number {
  return deg * (Math.PI/180);
}

/**
 * Enhanced distance calculation that tries route-based first, then falls back
 */
export async function calculateEnhancedDistance(
  origin: Coordinates | null,
  destination: Coordinates | null
): Promise<{ distance: number; source: string }> {
  // Validate coordinates
  if (!origin || !destination || !isValidCoordinate(origin) || !isValidCoordinate(destination)) {
    return { distance: 0, source: 'invalid' };
  }

  try {
    // Try route-based calculation
    const routeResult = await calculateRouteDistance(origin, destination);
    return {
      distance: routeResult.distance,
      source: routeResult.source
    };
  } catch (error) {
    console.error('Enhanced distance calculation failed:', error);
    
    // Final fallback to simple Haversine
    const distance = calculateHaversineDistance(origin, destination);
    return {
      distance,
      source: 'haversine-fallback'
    };
  }
}

/**
 * Validate coordinate object
 */
function isValidCoordinate(coord: Coordinates): boolean {
  return (
    typeof coord.lat === 'number' &&
    typeof coord.lng === 'number' &&
    !isNaN(coord.lat) &&
    !isNaN(coord.lng) &&
    coord.lat >= -90 &&
    coord.lat <= 90 &&
    coord.lng >= -180 &&
    coord.lng <= 180
  );
}

/**
 * Format distance for display
 */
export function formatDistance(distance: number, source?: string): string {
  if (distance === 0) return 'Distance unavailable';
  
  const formatted = distance < 1 
    ? `${Math.round(distance * 1000)}m away`
    : `${distance} km away`;
    
  // Add source indicator in development
  if (process.env.NODE_ENV === 'development' && source) {
    return `${formatted} (${source})`;
  }
  
  return formatted;
}
